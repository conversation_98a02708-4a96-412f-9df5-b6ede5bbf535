"use strict";

import { definedColumns, xAjax, reloadTable , components} from "../table";
$(document).ready(function() {
  var columns = [
    definedColumns.stt,
    definedColumns.order_code,
    definedColumns.url,
    definedColumns.count,
    definedColumns.price_per,
    definedColumns.total_payment,
    definedColumns.note_editable,
    definedColumns.admin_note,
    definedColumns.created_at,
    definedColumns.order_status,
  ];

  reloadTable.datatableLog = $('#datatable-ajax').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/orders/report/ajax`),
    order: [[ 0, "desc" ]],
    columns: columns
  });
});
