@extends('clients.layouts.service')

@section('title')
    {{ $categories->name }}
@endsection

@push('css')
    @include('admin.partials.datatables.style')
@endpush

@section('data')
    <div class="card">
        <ul class="nav nav-tabs nav-tabs-custom nav-success" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" data-bs-toggle="tab" href="#shopping-tab" role="tab" aria-selected="false"
                   tabindex="-1">
                    <i class="ri-shopping-bag-4-line"></i>
                    Mua gói dịch vụ
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link " data-bs-toggle="tab" href="#history-shopping" role="tab" aria-selected="true">
                    <i class="ri-history-line"></i>
                    Lịch sử mua
                </a>
            </li>
        </ul>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane fade active show" id="shopping-tab" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-body">
                                    <form id="formUserAction" action="/order/services">
                                        @if($uid ?? true)
                                            <div class="form-group">
                                                <input type="hidden" name="url" id="url">
                                                <label for="uid" class="form-label">ID hoặc link bài viết cần chạy</label>
                                                <input type="text" class="form-control" id="uid" name="uid"
                                                       autocomplete="off"
                                                       placeholder="Vui lòng nhập đúng link, sai link không hoàn tiền">
                                            </div>
                                        @endif
                                        @if($url ?? true)
                                                <div class="form-group">
                                                    <label for="url" class="form-label">ID hoặc link bài viết cần chạy</label>
                                                    <input type="text" class="form-control" id="url" name="url"
                                                           autocomplete="off"
                                                           placeholder="Vui lòng nhập đúng link, sai link không hoàn tiền">
                                                </div>
                                        @endif
                                        <div class="form-group">
                                            <label class="form-label">Chọn gói dịch vụ</label>
                                            @foreach($packages as $item)
                                                <div class="radio radio-package">
                                                    <label>
                                                        <input type="text" class="d-none" name="package_id" value="{{ $item->id }}">
                                                        <input type="radio" name="package"
                                                               value="{{ $item->packageList->key ?? $item->id }}">
                                                        {{ $item->packageList->name ?? 'Gói không xác định' }}
                                                    </label>
                                                </div>
                                            @endforeach
                                        </div>

                                        @if($showReactions ?? true)
                                            <div class="form-group">
                                                <label class="font-bold">Chọn loại cảm xúc:</label>
                                                <div class="fb-reaction m-t-10">
                                                    @php
                                                        $reactions = ['like', 'love', 'care', 'wow', 'haha', 'sad', 'angry'];
                                                        $reactionFieldName = $multipleReactions ?? false ? 'reaction[]' : 'reaction';
                                                    @endphp

                                                    @foreach($reactions as $reaction)
                                                        <div class="checkbox" data-reaction="{{ $reaction }}">
                                                            <label>
                                                                <img
                                                                    src="{{ asset('assets/images/reactions/' . $reaction . '.png') }}"
                                                                    alt="icon {{ $reaction }}" class="img-responsive">
                                                                <input name="{{ $reactionFieldName }}"
                                                                       value="{{ $reaction }}"
                                                                       type="checkbox" {{ $reaction === 'like' ? 'checked' : '' }}>
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                        @if($showComment ?? true)
                                            <div class="form-group">
                                                <label for="comments">
                                                    Danh Sách Bình Luận (
                                                    <span id="comment_count" class="text-bold">0</span>
                                                    )
                                                </label>
                                                <input type="hidden" name="count" id="count" class="form-control">
                                                <textarea class="form-control" name="comments" id="comments"
                                                          rows="10" required=""
                                                          placeholder="Mỗi dòng 1 bình luận."></textarea>
                                            </div>
                                        @endif
                                        @if($showCount ?? true)
                                            <div class="form-group">
                                                <label for="count" class="form-label">Số lượng</label>
                                                <input type="number" name="count" id="count" class="form-control">
                                            </div>
                                        @endif
                                        <div class="form-group">
                                            <label for="price" class="form-label">Giá tiền</label>
                                            <input type="text" name="price" id="price" class="form-control" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label for="total" class="form-label">Tổng giá</label>
                                            <input type="text" name="total" id="total" class="form-control" disabled>
                                        </div>
                                        <div class="form-group">
                                            <label for="note" class="form-label">Ghi chú</label>
                                            <input type="text" name="note" id="note" class="form-control">
                                        </div>

                                        <button id="submit" class="btn btn-success">
                                            Mua
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            @include('clients.services.partials.note')
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="history-shopping" role="tabpanel">
                    @include('clients.services.partials.history')
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    @include('admin.partials.datatables.script')
    <script>
        let platform = @json($platform);
        let category = @json($category);
    </script>
    <script>
        $("#comments").keyup(function () {
            var comments = $(this).val().trim();
            var count = comments.split("\n").filter(function (x) {
                return x.trim().length > 0
            }).length;
            $('#comment_count').html(count.toString());

            var price = $("#price").attr('data-price');
            if (count > 0) {
                var total = Number(count) * Number(price);
                $("#total").val(Base.formatMoney(total, ' VNĐ'));
            } else {
                $("#total").val(`0 VNĐ`);
            }
        });
    </script>
@endpush
