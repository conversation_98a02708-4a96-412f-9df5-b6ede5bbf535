@extends('clients.layouts.service')

@section('title')
    {{ $categories->name }}
@endsection
@push('css')
    @include('admin.partials.datatables.style')
@endpush
@section('data')
    <div class="card">
        <ul class="nav nav-tabs nav-tabs-custom nav-success" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" data-bs-toggle="tab" href="#shopping-tab" role="tab" aria-selected="false"
                   tabindex="-1">
                    <i class="ri-shopping-bag-4-line"></i>
                    Mua gói dịch vụ
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link " data-bs-toggle="tab" href="#history-shopping" role="tab" aria-selected="true">
                    <i class="ri-history-line"></i>
                    Lịch sử mua
                </a>
            </li>
        </ul>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane fade active show" id="shopping-tab" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-body">
                                    <form id="formUserAction" action="/order/services">
                                        <div class="form-group">
                                            <input type="hidden" name="url" id="url">
                                            <label for="uid" class="form-label">ID hoặc link bài viết cần chạy</label>
                                            <input type="text" class="form-control" id="uid" name="uid"
                                                   autocomplete="off"
                                                   placeholder="Vui lòng nhập đúng link, sai link không hoàn tiền">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Chọn gói dịch vụ</label>
                                            @foreach($packages as $item)
                                                <div class="radio radio-package">
                                                    <label>
                                                        <input type="hidden" name="package_id" value="{{ $item->id }}">
                                                        <input type="radio" name="package"
                                                               value="{{ $item->packageList->key ?? $item->id }}">
                                                        {{ $item->packageList->name ?? 'Gói không xác định' }}
                                                    </label>
                                                </div>
                                            @endforeach
                                        </div>
                                        <div class="form-group">
                                            <label class="font-bold">Chọn loại cảm xúc:</label>
                                            <div class="fb-reaction m-t-10">

                                                <div class="checkbox" data-reaction="like">
                                                    <label>
                                                        <img src="{{ asset('assets/images/reactions/like.png') }}"
                                                             alt="icon like" class="img-responsive">
                                                        <input name="reaction" value="like" type="checkbox" checked="">
                                                    </label>
                                                </div>

                                                <div class="checkbox" data-reaction="love">
                                                    <label>
                                                        <img src="{{ asset('assets/images/reactions/love.png') }}"
                                                             alt="icon love" class="img-responsive">
                                                        <input name="reaction" value="love" type="checkbox">
                                                    </label>
                                                </div>

                                                <div class="checkbox" data-reaction="care">
                                                    <label>
                                                        <img src="{{ asset('assets/images/reactions/care.png') }}"
                                                             alt="icon care" class="img-responsive">
                                                        <input name="reaction" value="care" type="checkbox">
                                                    </label>
                                                </div>

                                                <div class="checkbox" data-reaction="wow">
                                                    <label>
                                                        <img src="{{ asset('assets/images/reactions/wow.png') }}"
                                                             alt="icon wow" class="img-responsive">
                                                        <input name="reaction" value="wow" type="checkbox">
                                                    </label>
                                                </div>

                                                <div class="checkbox" data-reaction="haha">
                                                    <label>
                                                        <img src="{{ asset('assets/images/reactions/haha.png') }}"
                                                             alt="icon haha" class="img-responsive">
                                                        <input name="reaction" value="haha" type="checkbox">
                                                    </label>
                                                </div>

                                                <div class="checkbox" data-reaction="sad">
                                                    <label>
                                                        <img src="{{ asset('assets/images/reactions/sad.png') }}"
                                                             alt="icon sad" class="img-responsive">
                                                        <input name="reaction" value="sad" type="checkbox">
                                                    </label>
                                                </div>

                                                <div class="checkbox" data-reaction="angry">
                                                    <label>
                                                        <img src="{{ asset('assets/images/reactions/angry.png') }}"
                                                             alt="icon angry" class="img-responsive">
                                                        <input name="reaction" value="angry" type="checkbox">
                                                    </label>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="count" class="form-label">Số lượng</label>
                                            <input type="number" name="count" id="count" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="price" class="form-label">Giá tiền</label>
                                            <input type="number" name="price" id="price" class="form-control" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label for="total" class="form-label">Tổng giá</label>
                                            <input type="text" name="total" id="total" class="form-control" disabled>
                                        </div>
                                        <div class="form-group">
                                            <label for="note" class="form-label">Ghi chú</label>
                                            <input type="text" name="note" id="note" class="form-control">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            @include('clients.services.partials.note')
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="history-shopping" role="tabpanel">
                    @include('clients.services.partials.history')
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    @include('admin.partials.datatables.script')
    <script>
        let platform = @json($platform);
        let category = @json($category);
    </script>
@endpush

