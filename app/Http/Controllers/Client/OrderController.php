<?php

namespace App\Http\Controllers\Client;

use App\Enums\OrderStatusEnum;
use App\Enums\PackageStatusEnum;
use App\Enums\SourceStatusEnum;
use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Package;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\RateLimiter;

class OrderController extends Controller
{
    public function order(Request $request)
    {
        $payload = $request->validate([
            'count' => 'required|integer|min:1',
            'uid' => 'nullable',
            'url' => 'required|string|max:255',
            'package_id' => 'required|integer',
            'note' => 'nullable|string|max:255',
        ]);

        $userId = $request->user()->id ?? null;

        $user = $userId ? User::query()->find($userId) : null;
        if ($user === null) {
            return response()->json([
                'data' => [
                    'status' => 400,
                    'message' => 'Không tìm thấy tài khoản',
                ]
            ], 400);
        }
        if (!RateLimiter::attempt(
            "store-order:{$user->id}",
            $perMinute = 1,
            function () {
            },
            $decayRate = 1
        )) {
            return response()->json([
                'data' => [
                    'status' => 429,
                    'message' => 'Quá nhiều yêu cầu'
                ]
            ], 429);
        }

        if ($user->status->value !== UserStatusEnum::ACTIVE->value) {
            return response()->json([
                'data' => [
                    'status' => 403,
                    'message' => 'Tài khoản đã bị khoá',
                ]
            ], 403);
        }
        $package = Package::query()
            ->where('status', PackageStatusEnum::ACTIVE->value)
            ->orWhere('status', PackageStatusEnum::SLOW->value)
            ->find($payload['package_id']);

        if ($package === null) {
            return response()->json([
                'data' => [
                    'status' => 400,
                    'message' => 'Không tìm thấy máy chủ',
                ]
            ], 400);
        }
        $category_id = $package->category_id;
        if (!$category_id) {
            return response()->json([
                'data' => [
                    'status' => 400,
                    'message' => 'Máy chủ không thuộc danh mục nào',
                ]
            ], 400);
        }
        $count = $payload['count'];
        $price_per_raw = $package->getPriceForRank($user->level->value);
        if (is_string($price_per_raw)) {
            $price_per_clean = preg_replace('/[^\d.]/', '', $price_per_raw);
            $price_per = (float)$price_per_clean;
        } else {
            $price_per = (float)$price_per_raw;
        }
        $url = $payload['url'];
        $package_id = $payload['package_id'];
        $commentsArr = [];

        if ($package->service_type === 'Custom Comments' || $package->service_type === 'Custom Comment') {
            $payload = array_merge($payload, $request->validate([
                'comments' => 'required|string|max:5000',
            ]));

            $commentsArr = textToArray($payload['comments']);

            if (count($commentsArr) === 0) {
                return response()->json([
                    'status' => 422,
                    'message' => 'Vui lòng nhập bình luận',
                ], 422);
            }

            $count = (int)count($commentsArr);
        }
        $reaction = '';

        if ($package->allow_reaction == 1 || $package->multiple_reaction == 1) {
            $payload = array_merge($payload, $request->validate([
                'reaction' => 'required|array',
                'reaction.*' => 'string|max:50',
            ]));

            $reaction = implode('|', $payload['reaction']);
        }

        if ($package->min > 0 && $count < $package->min) {
            return response()->json([
                'data' => [
                    'status' => 400,
                    'message' => ' Số lượng tối thiểu: ' . $package->min,
                ]
            ], 400);
        }

        if ($package->max > 0 && $count > $package->max) {
            return response()->json([
                'data' => [
                    'status' => 400,
                    'message' => ' Số lượng tối đa: ' . $package->max,
                ]
            ], 400);
        }


        $totalPayment = $count * $price_per;

        if ($totalPayment <= 0) {
            return response()->json([
                'data' => [
                    'status' => 400,
                    'message' => 'Số tiền thanh toán không hợp lệ',
                ]
            ], 400);
        }

        if ($user->balance < $totalPayment) {
            return response()->json([
                'data' => [
                    'status' => 400,
                    'message' => 'Số dư không đủ để thực hiện giao dịch',
                ]
            ], 400);
        }
        $balanceBefore = $user->balance;

        // Trừ tiền
        if ($user->decrement('balance', $totalPayment) === false) {
            return response()->json([
                'data' => [
                'status' => 500,
                'message' => 'Thanh toán không thành công',
                    ]
            ], 500);
        }
        $tenantID = session('current_tenant_id');
        $user->increment('total_spent', $totalPayment);
        $order = new Order();
        $order->user_id = $user->id;
        $order->category_id = $category_id;
        $order->package_id = $package->id;
        $order->tenant_id = $tenantID;
        $order->source_id = -1;
        $order->source_name = $package->api_provider_id;
        $order->source_type = $package->service_id;
        $order->source_cost = $package->rate * $count;
        $order->source_place = false;
        $order->source_status = 'Pending';
        $order->order_code = random_int(100000, 999999);
        $order->url = $url;
        $order->uid = $payload['uid'] ?? null;
        $order->count = $count;
        $order->start_number = 0;
        $order->success_count = 0;
        $order->price_per = $price_per;
        $order->total_payment = $totalPayment;
        $order->currency_code = 'VND';
        $order->note = $payload['note'] ?? null;
        $order->order_status = OrderStatusEnum::PENDING->value;
        $order->extra_data = [
            'count' => $count,
            'comments' => $commentsArr,
            'url' => $url,
            'reaction' => $reaction,
        ];
        $order->message = 'Mua ' . $package->category->name;
        $order->save();

        Transaction::query()
            ->create([
                'user_id' => $user->id,
                'tenant_id' => $tenantID,
                'admin_id' => $user->id,
                'type' => TransactionTypeEnum::DEPOSIT->value,
                'amount' => $totalPayment,
                'balance_before' => $balanceBefore,
                'balance_after' => $user->balance,
                'status' => TransactionStatusEnum::COMPLETED->value,
                'description' => 'Thanh toán đơn hàng #' . $order->order_code,
                'math' => '-'
            ]);

//        $postData = [
//            'key' => $order->provider->key,
//            'link' => $order->uid ?? $order->url,
//            'action' => "add",
//            'service' => $order->source_type,
//            'quantity' => $order->count,
//            'comments' => implode("\n", $order->extra_data['comments'] ?? []),
//            'reaction' => $order->extra_data['reaction'] ?? null,
//        ];
//        $result = Http::timeout(60)->asForm()->post($order->provider->url, $postData)->json();
//        if (isset($result['error'])) {
//            $order->update([
//                'source_resp' => $result,
//                'source_place' => true,
//                'extra_note' => $result['error'] ?? '',
//                'source_status' => SourceStatusEnum::PENDING->value,
//                'order_status' => OrderStatusEnum::PENDING->value,
//            ]);
//        } else {
//            $orderId = $result['order'] ?? null;
//            if ($orderId === null) {
//                $orderId = $result['order_id'] ?? null;
//            }
//            if ($orderId === null) {
//                $orderId = -3;
//            }
//            $order->update([
//                'source_id' => $orderId,
//                'source_resp' => $result,
//                'source_place' => true,
//                'source_status' => SourceStatusEnum::PROCESSING->value,
//                'order_status' => OrderStatusEnum::PROCESSING->value,
//            ]);
//        }
        return response()->json([
            'status' => 200,
            'message' => 'Đặt đơn hàng thành công',
            'data' => [
                'order_id' => $order->id,
                'order_code' => $order->order_code
            ]
        ], 200);
    }
}
