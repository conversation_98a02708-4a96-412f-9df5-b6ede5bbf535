<?php

namespace App\Http\Controllers\Admin;

use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Enums\TypeNotificationEnum;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Yajra\DataTables\Facades\DataTables;

class HandleMoneyMemberController extends Controller
{
    public function showFormPlusMoney(Request $request)
    {
        \Assets::addScriptsDirectly('assets/libs/select2/js/select2.min.js');
        $tenantID = session('current_tenant_id');
        $users = User::whereHas('tenants', function ($query) use ($tenantID) {
            $query->where('tenant_id', $tenantID)
                ->where('role', 'user');
        })
            ->where('status', UserStatusEnum::ACTIVE->value)
            ->get();

        if ($request->ajax()) {
            $transactions = Transaction::with(['user' => function ($query) {
                $query->select('id', 'username');
            }])
                ->where('tenant_id', $tenantID)
                ->where('type', TransactionTypeEnum::PLUS_MONEY->value)
                ->select('id', 'user_id', 'amount', 'created_at', 'status', 'description', 'math')
                ->orderBy('created_at', 'desc');

            return DataTables::of($transactions)
                ->addColumn('username', function ($transaction) {
                    return '<span class="text-plain">' . $transaction->user->username . '</span>' ?? 'N/A';
                })
                ->editColumn('amount', function ($transaction) {
                    return '<span class="badge bg-success">' . $transaction->math . number_format($transaction->amount) . '</span>';
                })
                ->editColumn('created_at', function ($transaction) {
                    return $transaction->created_at->format('d/m/Y');
                })
                ->editColumn('description', function ($transaction) {
                    return $transaction->description ?? 'Không có mô tả';
                })
                ->editColumn('status', function ($transaction) {
                    return $transaction->status->toHtml() ?? 'N/A';
                })
                ->rawColumns(['amount', 'username', 'status'])
                ->make(true);
        }
        return view('admin.members.plus-money', [
            'users' => $users,
        ]);
    }


    public function plushMoney(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:255',
        ]);

        $tenant = $request->tenant;
        $adminID = auth()->id();

        $user = User::where('id', $request->user_id)
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->lockForUpdate()
            ->firstOrFail();

        Transaction::query()->create([
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
            'admin_id' => $adminID,
            'type' => TransactionTypeEnum::PLUS_MONEY->value,
            'amount' => $request->amount,
            'balance_before' => $user->balance,
            'balance_after' => $user->balance + $request->amount,
            'description' => $request->description,
            'status' => TransactionStatusEnum::COMPLETED->value,
            'math' => '+',
        ]);

        $user->increment('balance', $request->amount);
        $user->increment('total_deposit', $request->amount);

        Notification::query()->create([
            'title' => 'Cộng tiền thủ công',
            'content' => 'Cộng thủ công <span class="text-orange fw-semibold">' . number_format($request->amount) . '</span>',
            'tenant_id' => $tenant->id,
            'user_id' => $user->id,
            'is_read' => false,
            'amount' => $request->amount,
            'type' => TypeNotificationEnum::PLUS_MONEY->value,
        ]);
        return redirect()->route('admin.members.plus-money')->with('success_msg', 'Cộng tiền thành công');
    }

    public function showFormMinusMoney(Request $request)
    {
        \Assets::addScriptsDirectly('assets/libs/select2/js/select2.min.js');
        $tenantID = session('current_tenant_id');
        $users = User::whereHas('tenants', function ($query) use ($tenantID) {
            $query->where('tenant_id', $tenantID)
                ->where('role', 'user');
        })
            ->where('status', UserStatusEnum::ACTIVE->value)
            ->get();

        if ($request->ajax()) {
            $transactions = Transaction::with(['user' => function ($query) {
                $query->select('id', 'username');
            }])
                ->where('tenant_id', $tenantID)
                ->where('type', TransactionTypeEnum::MINUS_MONEY->value)
                ->select('id', 'user_id', 'amount', 'created_at', 'status', 'description', 'math')
                ->orderBy('created_at', 'desc');

            return DataTables::of($transactions)
                ->addColumn('username', function ($transaction) {
                    return '<span class="text-plain">' . $transaction->user->username . '</span>' ?? 'N/A';
                })
                ->editColumn('amount', function ($transaction) {
                    return '<span class="badge bg-pink">' . $transaction->math . number_format($transaction->amount) . '</span>';
                })
                ->editColumn('created_at', function ($transaction) {
                    return $transaction->created_at->format('d/m/Y');
                })
                ->editColumn('description', function ($transaction) {
                    return $transaction->description ?? 'Không có mô tả';
                })
                ->editColumn('status', function ($transaction) {
                    return $transaction->status->toHtml() ?? 'N/A';
                })
                ->rawColumns(['amount', 'username', 'status'])
                ->make(true);
        }
        return view('admin.members.minus-money', [
            'users' => $users,
        ]);
    }

    public function minusMoney(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
        ]);

        $tenant = $request->tenant;
        $adminID = auth()->id();

        $user = User::where('id', $request->user_id)
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->lockForUpdate()
            ->firstOrFail();
        if ($user->balance < $request->amount) {
            return redirect()->back()->with('error_msg', 'Số dư không đủ để thực hiện giao dịch.');
        }
        Transaction::query()->create([
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
            'admin_id' => $adminID,
            'type' => TransactionTypeEnum::MINUS_MONEY->value,
            'amount' => $request->amount,
            'balance_before' => $user->balance,
            'balance_after' => $user->balance - $request->amount,
            'description' => $request->description,
            'status' => TransactionStatusEnum::COMPLETED->value,
            'math' => '-',
        ]);

        $user->decrement('balance', $request->amount);

        Notification::query()->create([
            'title' => 'Trừ tiền thủ công',
            'content' => 'Trừ thủ công <span class="text-orange fw-semibold">' . number_format($request->amount) . '</span>',
            'tenant_id' => $tenant->id,
            'user_id' => $user->id,
            'is_read' => false,
            'amount' => $request->amount,
            'type' => TypeNotificationEnum::MINUS_MONEY->value,
        ]);

        return redirect()->back()->with('success_msg', 'Trừ tiền thành công');
    }

}
