<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\UserLevelEnum;
use App\Enums\UserStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'full_name',
        'username',
        'email',
        'password',
        'phone',
        'dob',
        'avatar',
        'balance',
        'total_spent',
        'total_deposit',
        'level',
        'status',
        'timezone',
        'locale',
        'api_token'
    ];

    protected $casts = [
        'status' => UserStatusEnum::class,
        'level' => UserLevelEnum::class,
    ];
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($user) {
            do {
                $number = now()->format('Ymd') . rand(1000, 9999);
            } while (self::where('code', $number)->exists());

            $user->code = $number;
        });
    }

    public function ownedTenants(): HasMany
    {
        return $this->hasMany(Tenant::class, 'owner_id');
    }

    public function tenants(): BelongsToMany
    {
        return $this->belongsToMany(Tenant::class, 'tenant_users')
            ->withPivot('role');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function activityLogs(): HasMany
    {
        return $this->hasMany(UserActivityLog::class);
    }

    public function isOwner()
    {
        return $this->ownedTenants()->exists();
    }

    public function scopeForTenant($query, $tenantId)
    {
        return $query->whereHas('tenants', function ($q) use ($tenantId) {
            $q->where('tenant_id', $tenantId);
        });
    }

    public function getRoleInTenant($tenant): ?string
    {
        $tenantUser = $this->tenants()->where('tenant_id', $tenant->id)->first();
        return $tenantUser ? $tenantUser->pivot->role : null;
    }

    public function isAdminInTenant($tenant): bool
    {
        return in_array($this->getRoleInTenant($tenant), ['admin', 'super-admin']);
    }

    public function isSuperAdmin(): bool
    {
        return $this->tenants()
            ->where('tenant_id', 1)
            ->wherePivot('role', 'super-admin')
            ->exists();
    }

    public function canAccessTenant($tenant): bool
    {
        return $this->tenants()->where('tenant_id', $tenant->id)->exists();
    }

    
}
