const mix = require("laravel-mix");
const lodash = require("lodash");

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

const third_party_assets = {
    css_js: [
        { name: "jquery", assets: ["./node_modules/jquery/dist/jquery.js"] },
        { name: "bootstrap", assets: ["./node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"] },
        { name: "simplebar", assets: ["./node_modules/simplebar/dist/simplebar.min.js"] },
    ],
};

lodash(third_party_assets).forEach(function (assets, type) {
    if (type === "css_js") {
        lodash(assets).forEach(function (plugin) {
            let name = plugin["name"],
                assetlist = plugin["assets"],
                css = [],
                js = [];
            lodash(assetlist).forEach(function (asset) {
                let ass = asset.split(",");
                for (let i = 0; i < ass.length; ++i) {
                    if (ass[i].substr(ass[i].length - 3) === ".js") {
                        js.push(ass[i]);
                    } else {
                        css.push(ass[i]);
                    }
                }
            });
            if (js.length > 0) {
                mix.combine(js, "public/assets" + "/libs/" + name + "/" + name + ".min.js");
            }
            if (css.length > 0) {
                mix.combine(css, "public/assets" + "/libs/" + name + "/" + name + ".min.css");
            }
        });
    }
});

const app_pages_assets = {
    js: []
};

lodash(app_pages_assets).forEach(function(assets, type) {
    for (let i = 0; i < assets.length; ++i) {
        mix.js(assets[i], "public/assets/js/pages");
    }
});

mix.copyDirectory("resources/fonts", "public/assets/fonts");
mix.copyDirectory("resources/images", "public/assets/images");
mix.sass("resources/sass/icons.scss", "public/assets/css/icons.min.css");
mix.sass("resources/sass/bootstrap.scss", "public/assets/css");
mix.sass("resources/sass/app.scss", "public/assets/css").options({ processCssUrls: false });

mix.js("resources/js/bootstrap.js", "public/assets/js/bootstrap.min.js");
mix.js("resources/js/app.js", "public/assets/js/app.min.js");
mix.js("resources/js/init.js", "public/assets/js/init.min.js");
mix.js("resources/js/table.js", "public/assets/js/table.min.js");
mix.js("resources/js/packages.js", "public/assets/js/packages.min.js");
mix.js("resources/js/services.js", "public/assets/js/services.min.js");
mix.js("resources/js/preload.js", "public/assets/js/preload.min.js");
mix.js("resources/js/common.js", "public/assets/js/common.min.js");
mix.js("resources/js/clients/datatable-report.js", "public/assets/js/clients/datatable-report.min.js");
mix.js("resources/js/clients/datatable-order.js", "public/assets/js/clients/datatable-order.min.js");
mix.js("resources/js/clients/datatable-history.js", "public/assets/js/clients/datatable-history.min.js");
mix.js("resources/js/supper-admin/datatable-category.js", "public/assets/js/supper-admin/datatable-category.min.js");
mix.js("resources/js/supper-admin/datatable-platform.js", "public/assets/js/supper-admin/datatable-platform.min.js");
mix.js("resources/js/supper-admin/datatable-provider.js", "public/assets/js/supper-admin/datatable-provider.min.js");
mix.js("resources/js/supper-admin/datatable-package-list.js", "public/assets/js/supper-admin/datatable-package-list.min.js");
mix.js("resources/js/supper-admin/datatable-package.js", "public/assets/js/supper-admin/datatable-package.min.js");
mix.js("resources/js/supper-admin/datatable-transaction.js", "public/assets/js/supper-admin/datatable-transaction.min.js");
